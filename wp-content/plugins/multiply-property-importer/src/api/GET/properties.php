<?php

use Multiply\Models\Property;
use Multiply\Models\Properties;


add_action('rest_api_init', function () {
  register_rest_route('multiply/v1', '/properties', array(
    'methods' => 'GET',
    'callback' => 'get_properties',
  ));
});


function get_properties($data)
{
  $get_params = $data->get_params();

  $search = $get_params['search'];
  $listing_type = $get_params['listingType'];
  $property_type = $get_params['propertyType'];
  $location = $get_params['location'];
  $min_price = $get_params['minPrice'];
  $max_price = $get_params['maxPrice'];
  $other_type = $get_params['otherPropertyType'];
  $owner = $get_params['owner'];
  $order = $get_params['order'];
  $keywords = $get_params['keywords'];

  $current_page = $get_params['page'] ? $get_params['page'] : 1;

  $repository = new PropertiesRepository();

  $properties = get_cached_properties();

  if (!$properties) {
    return new WP_Error('no_properties', 'No properties found', array('status' => 404));
  }
  $all_properties = $repository->getFilteredProperties($search, $listing_type, $property_type, $location, $min_price, $max_price, $other_type, $keywords, $owner, $order);

  $count = count($all_properties);

  $per_page = 12;
  $total_pages = ceil($count / $per_page);
  $current_page = $current_page > $total_pages ? $total_pages : $current_page;
  $offset = ($current_page - 1) * $per_page;

  $properties = array_slice($all_properties, $offset, $per_page);

  return array(
    'properties' => $properties,
    'total_pages' => $total_pages,
    'current_page' => $current_page,
    'total_properties' => $count
  );
}
