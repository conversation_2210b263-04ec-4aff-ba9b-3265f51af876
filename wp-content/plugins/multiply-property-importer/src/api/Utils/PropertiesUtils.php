<?php
use Multiply\Models\Properties;
function get_cached_properties(): Properties
{

  $cached_properties = get_transient('cached_properties_');

  if ($cached_properties) {
    return $cached_properties;
  } else {
    AutoImport::run();
    return get_transient('cached_properties_');
  }
}

function get_cached_active_properties(): Properties
{

  $cached_properties = get_transient('cached_active_properties_');

  if ($cached_properties) {
    return $cached_properties;
  } else {
    AutoImport::run();
    return get_transient('cached_active_properties_');
  }
}


function get_distinct_property_types($properties)
{
  $property_types = array();
  foreach ($properties as $property) {
    $property_property_types = $property->propertyTypes;
    foreach ($property_property_types as $property_type) {
      if (!in_array($property_type, $property_types)) {
        $property_types[] = $property_type;
      }
    }
  }
  return $property_types;
}

// API Endpoint to get distinct property types
add_action('rest_api_init', function () {
  register_rest_route('multiply/v1', '/property-types', array(
    'methods' => 'GET',
    'callback' => 'get_property_types',
  ));
});

function get_property_types()
{
  $properties = get_cached_properties();

  if (!$properties) {
    return new WP_Error('no_properties', 'No properties found', array('status' => 404));
  }

  $property_types = get_distinct_property_types($properties);

  return $property_types;
}

// API Endpoint to get distinct property types that are not the first ones in each property
add_action('rest_api_init', function () {
  register_rest_route('multiply/v1', '/other-types', array(
    'methods' => 'GET',
    'callback' => 'get_property_other_types',
  ));
});

function get_property_other_types()
{
  $properties = get_cached_properties();

  if (!$properties) {
    return new WP_Error('no_properties', 'No properties found', array('status' => 404));
  }

  $property_types = array();
  foreach ($properties as $property) {
    $property_property_types = $property->propertyTypes;
    if (count($property_property_types) > 1) {
      for ($i = 1; $i < count($property_property_types); $i++) {
        if (!in_array($property_property_types[$i], $property_types)) {
          $property_types[] = $property_property_types[$i];
        }
      }
    }
  }

  return $property_types;
}