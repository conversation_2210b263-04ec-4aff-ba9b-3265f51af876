<?php

use Multiply\Models\Properties;

class AutoImport
{
  const CACHE_KEY = 'cached_properties_';
  const ACTIVE_CACHE_KEY = 'cached_active_properties_';
  const DATA_FILE = __DIR__ . '/properties_data.json';
  const ACTIVE_DATA_FILE = __DIR__ . '/active_properties_data.json';

  public static function run()
  {
    // Fetch and cache all properties
    $propertiesData = self::fetchPropertiesData();
    if (!$propertiesData) {
      $propertiesData = self::loadFromDisk(self::DATA_FILE);
      if (!$propertiesData) {
        error_log('Failed to fetch or load properties data.');
        return;
      }
    }

    $properties = Properties::fromJson($propertiesData);
    self::saveToDisk(self::DATA_FILE, $propertiesData);
    self::setCache(self::CACHE_KEY, $properties);

    // Fetch and cache active properties
    $activePropertiesData = self::fetchActivePropertiesData();
    if (!$activePropertiesData) {
      $activePropertiesData = self::loadFromDisk(self::ACTIVE_DATA_FILE);
      if (!$activePropertiesData) {
        error_log('Failed to fetch or load active properties data.');
        return;
      }
    }

    $activeProperties = Properties::fromJson($activePropertiesData);
    self::saveToDisk(self::ACTIVE_DATA_FILE, $activePropertiesData);
    self::setCache(self::ACTIVE_CACHE_KEY, $activeProperties);
  }

  private static function fetchPropertiesData(): ?array
  {
    try {
      $response = getAllProperties();
      if ($response === false) {
        throw new Exception('Failed to fetch properties data from network.');
      }
      return $response;
    } catch (Exception $e) {
      error_log($e->getMessage());
      return null;
    }
  }

  private static function fetchActivePropertiesData(): ?array
  {
    try {
      $response = getAllActiveProperties();
      if ($response === false) {
        throw new Exception('Failed to fetch active properties data from network.');
      }
      return $response;
    } catch (Exception $e) {
      error_log($e->getMessage());
      return null;
    }
  }

  private static function loadFromDisk(string $filePath): ?array
  {
    if (file_exists($filePath)) {
      $data = file_get_contents($filePath);
      return json_decode($data, true);
    }
    return null;
  }

  private static function saveToDisk(string $filePath, array $data): void
  {
    // Save data to disk, make sure it exists first
    if (!file_exists($filePath)) {
      touch($filePath);
    }
    file_put_contents($filePath, json_encode($data));
  }

  private static function getCache(string $cacheKey): ?Properties
  {
    $cached = get_transient($cacheKey);
    if ($cached) {
      return $cached;
    }
    return null;
  }

  private static function setCache(string $cacheKey, Properties $properties): void
  {
    set_transient($cacheKey, $properties, 60 * 60 * 1); // 1 hour
  }
}

function run_auto_import()
{
  AutoImport::run();
  error_log('Auto Import Run at: ' . date('Y-m-d H:i:s'));
}

if (!wp_next_scheduled('properties_auto_import')) {
  wp_schedule_event(time(), 'hourly', 'properties_auto_import');
}
add_action('properties_auto_import', 'run_auto_import');